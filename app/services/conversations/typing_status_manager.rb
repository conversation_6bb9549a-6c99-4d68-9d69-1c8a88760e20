class Conversations::TypingStatusManager
  include Events::Types

  attr_reader :conversation, :user, :params

  def initialize(conversation, user, params)
    @conversation = conversation
    @user = user
    @params = params
  end

  def toggle_typing_status
    case params[:typing_status]
    when 'on'
      trigger_typing_event(CONVERSATION_TYPING_ON)
    when 'off'
      trigger_typing_event(CONVERSATION_TYPING_OFF)
    end
  end

  private

  def trigger_typing_event(event)
    # Dispatch event cho UI (ActionCable)
    Rails.configuration.dispatcher.dispatch(
      event,
      Time.zone.now,
      conversation: @conversation,
      user: @user,
      is_private: params[:is_private] || false
    )

    # Chỉ gửi typing indicator đến platform khi là human agent (User)
    # Bỏ logic bot agent phức tạp, chỉ focus vào human agent
    if @user.is_a?(User) && @user.agent?
      send_platform_typing_indicator(event)
    end
  end

  def send_platform_typing_indicator(event)
    Rails.logger.info "TypingStatusManager: Checking typing indicator for conversation #{@conversation.id}, channel: #{@conversation.inbox.channel_type}"

    return unless @conversation.inbox.channel_type.in?(['Channel::FacebookPage', 'Channel::Instagram'])

    if @conversation.contact.blank?
      Rails.logger.error "TypingStatusManager: Contact is blank for conversation #{@conversation.id}"
      return
    end

    # Debug thông tin contact và source_id
    source_id = @conversation.contact.get_source_id(@conversation.inbox.id) rescue nil
    Rails.logger.info "TypingStatusManager: Contact ID: #{@conversation.contact.id}, Source ID: #{source_id}"

    if source_id.blank?
      Rails.logger.error "TypingStatusManager: Source ID is blank for contact #{@conversation.contact.id} in inbox #{@conversation.inbox.id}"
      return
    end

    begin
      typing_service = Bot::TypingService.new(conversation: @conversation)

      case event
      when CONVERSATION_TYPING_ON
        Rails.logger.info "TypingStatusManager: Attempting to enable typing for #{@conversation.inbox.channel_type}"
        result = typing_service.enable_typing
        Rails.logger.info "TypingStatusManager: Human agent typing ON sent to #{@conversation.inbox.channel_type} - Success: #{result}"
      when CONVERSATION_TYPING_OFF
        Rails.logger.info "TypingStatusManager: Attempting to disable typing for #{@conversation.inbox.channel_type}"
        result = typing_service.disable_typing
        Rails.logger.info "TypingStatusManager: Human agent typing OFF sent to #{@conversation.inbox.channel_type} - Success: #{result}"
      end
    rescue => e
      Rails.logger.error "TypingStatusManager: Error sending human agent typing to platform: #{e.message}"
      Rails.logger.error "TypingStatusManager: Backtrace: #{e.backtrace.first(5).join('\n')}"
    end
  end
end
